"""
专门分析 write_videofile 性能的脚本
使用 line_profiler 进行逐行分析
"""
import sys
from pathlib import Path
import subprocess
import time

def install_line_profiler():
    """安装 line_profiler"""
    print("安装 line_profiler...")
    try:
        subprocess.run([sys.executable, "-m", "pip", "install", "line_profiler"], 
                      check=True)
        print("✅ line_profiler 安装成功")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ line_profiler 安装失败: {e}")
        return False

def create_profiled_version():
    """创建添加了性能分析装饰器的版本"""
    
    # 读取原始文件
    with open("enhanced_generator.py", 'r', encoding='utf-8') as f:
        content = f.read()
    
    # 在 _finalize_and_export_video 方法前添加 @profile 装饰器
    modified_content = content.replace(
        'def _finalize_and_export_video(',
        '@profile\n    def _finalize_and_export_video('
    )
    
    # 保存修改后的版本
    with open("enhanced_generator_profiled.py", 'w', encoding='utf-8') as f:
        f.write(modified_content)
    
    return "enhanced_generator_profiled.py"

def create_test_script():
    """创建测试脚本"""
    script_content = '''
import sys
from pathlib import Path

# 使用性能分析版本
from enhanced_generator_profiled import demo_enhanced_features

if __name__ == "__main__":
    print("开始 write_videofile 性能分析...")
    
    config_path = Path(r"精武英雄\\lrc-mv.yaml")
    
    # 使用较短时长进行快速分析
    success = demo_enhanced_features(
        config_path=config_path, 
        t_max_sec=15.0,  # 15秒视频
        quality_profile="draft"  # 使用草稿质量
    )
    
    print(f"分析完成，结果: {'成功' if success else '失败'}")
'''
    
    with open("test_write_videofile_performance.py", 'w', encoding='utf-8') as f:
        f.write(script_content)
    
    return "test_write_videofile_performance.py"

def run_line_profiler_analysis():
    """运行 line_profiler 分析"""
    print("创建性能分析版本...")
    profiled_file = create_profiled_version()
    test_script = create_test_script()
    
    print("运行 line_profiler 分析...")
    try:
        # 运行 kernprof
        result = subprocess.run([
            "kernprof", "-l", "-v", test_script
        ], capture_output=True, text=True, timeout=300)
        
        print("line_profiler 分析结果:")
        print("="*50)
        print(result.stdout)
        
        if result.stderr:
            print("错误输出:")
            print(result.stderr)
            
        # 保存结果
        with open("line_profiler_results.txt", 'w', encoding='utf-8') as f:
            f.write("write_videofile 性能分析结果\\n")
            f.write("="*50 + "\\n")
            f.write(result.stdout)
            if result.stderr:
                f.write("\\n错误输出:\\n")
                f.write(result.stderr)
        
        print(f"\\n✅ 结果已保存到 line_profiler_results.txt")
        
    except subprocess.TimeoutExpired:
        print("⚠️ 分析超时")
    except FileNotFoundError:
        print("❌ kernprof 命令未找到，请确保 line_profiler 正确安装")
    except Exception as e:
        print(f"❌ 分析失败: {e}")
    finally:
        # 清理临时文件
        Path(profiled_file).unlink(missing_ok=True)
        Path(test_script).unlink(missing_ok=True)
        Path(f"{test_script}.lprof").unlink(missing_ok=True)

def run_simple_timing_analysis():
    """运行简单的计时分析"""
    print("\\n运行简单计时分析...")
    
    script_content = '''
import time
import sys
from pathlib import Path
from enhanced_generator import EnhancedJingwuGenerator, demo_enhanced_features

def timed_demo():
    """带计时的演示函数"""
    
    print("开始详细计时分析...")
    start_total = time.perf_counter()
    
    config_path = Path(r"精武英雄\\lrc-mv.yaml")
    
    # 分段计时
    success = demo_enhanced_features(
        config_path=config_path, 
        t_max_sec=20.0,
        quality_profile="draft"
    )
    
    end_total = time.perf_counter()
    total_time = end_total - start_total
    
    print(f"\\n总耗时: {total_time:.2f} 秒")
    
    return success

if __name__ == "__main__":
    timed_demo()
'''
    
    with open("simple_timing_analysis.py", 'w', encoding='utf-8') as f:
        f.write(script_content)
    
    try:
        result = subprocess.run([
            sys.executable, "simple_timing_analysis.py"
        ], capture_output=True, text=True, timeout=300)
        
        print("计时分析结果:")
        print(result.stdout)
        
        if result.stderr:
            print("错误:")
            print(result.stderr)
            
    except Exception as e:
        print(f"计时分析失败: {e}")
    finally:
        Path("simple_timing_analysis.py").unlink(missing_ok=True)

def main():
    """主函数"""
    print("write_videofile 性能分析工具")
    print("="*50)
    print("这个工具将帮助分析 MoviePy write_videofile 的性能瓶颈")
    print()
    
    # 检查 line_profiler 是否安装
    try:
        subprocess.run(["kernprof", "--help"], capture_output=True, check=True)
        print("✅ line_profiler 已安装")
        use_line_profiler = True
    except (subprocess.CalledProcessError, FileNotFoundError):
        print("❌ line_profiler 未安装")
        install_choice = input("是否安装 line_profiler? (y/n): ").lower()
        if install_choice == 'y':
            use_line_profiler = install_line_profiler()
        else:
            use_line_profiler = False
    
    print("\\n选择分析方法:")
    if use_line_profiler:
        print("1. line_profiler 逐行分析 (推荐 - 精确定位性能瓶颈)")
    print("2. 简单计时分析 (快速了解整体耗时)")
    print("3. 两种方法都使用")
    
    choice = input("请选择: ").strip()
    
    if use_line_profiler and choice in ['1', '3']:
        run_line_profiler_analysis()
    
    if choice in ['2', '3']:
        run_simple_timing_analysis()
    
    print("\\n" + "="*50)
    print("分析建议:")
    print("1. 检查 write_videofile 调用时的参数配置")
    print("2. 注意 FFmpeg 编码器的选择 (h264_nvenc vs libx264)")
    print("3. 观察 temp_audiofile 的处理时间")
    print("4. 检查视频片段数量是否过多")
    print("5. 考虑使用更快的编码预设")

if __name__ == "__main__":
    main()
