"""
专门分析 write_videofile 的性能脚本
使用 cProfile 进行详细的函数级别分析
"""
import cProfile
import pstats
import io
import sys
from pathlib import Path
import time

sys.path.insert(0, str(Path(__file__).parent))

from enhanced_generator import demo_enhanced_features

def profile_write_videofile():
    """使用 cProfile 分析 write_videofile 性能"""
    
    print("开始 cProfile 性能分析...")
    print("="*50)
    
    # 创建性能分析器
    profiler = cProfile.Profile()
    
    # 开始分析
    profiler.enable()
    
    start_time = time.perf_counter()
    
    # 执行目标函数
    config_path = Path(r"精武英雄\lrc-mv.yaml")
    success = demo_enhanced_features(
        config_path=config_path,
        t_max_sec=15.0,
        quality_profile="draft"
    )
    
    end_time = time.perf_counter()
    
    # 停止分析
    profiler.disable()
    
    total_time = end_time - start_time
    print(f"\n总执行时间: {total_time:.2f} 秒")
    print(f"执行结果: {'成功' if success else '失败'}")
    
    # 创建输出目录
    output_dir = Path("performance_analysis")
    output_dir.mkdir(exist_ok=True)
    
    # 保存原始性能数据
    prof_file = output_dir / "write_videofile_profile.prof"
    profiler.dump_stats(str(prof_file))
    print(f"\n✅ 原始性能数据保存到: {prof_file}")
    
    # 生成详细报告
    report_file = output_dir / "write_videofile_analysis.txt"
    
    with open(report_file, 'w', encoding='utf-8') as f:
        f.write("write_videofile 性能分析报告\n")
        f.write("="*50 + "\n")
        f.write(f"总执行时间: {total_time:.2f} 秒\n")
        f.write(f"执行结果: {'成功' if success else '失败'}\n")
        f.write("\n")
        
        # 创建统计对象
        s = pstats.Stats(profiler, stream=f)
        
        # 按累积时间排序，显示前30个函数
        f.write("按累积时间排序的前30个函数:\n")
        f.write("-"*50 + "\n")
        s.sort_stats('cumulative')
        s.print_stats(30)
        
        # 按总时间排序
        f.write("\n\n按总时间排序的前20个函数:\n")
        f.write("-"*50 + "\n")
        s.sort_stats('tottime')
        s.print_stats(20)
        
        # 专门查看 MoviePy 相关函数
        f.write("\n\nMoviePy 相关函数:\n")
        f.write("-"*50 + "\n")
        s.print_stats('moviepy')
        
        # 查看 write_videofile 相关
        f.write("\n\nwrite_videofile 相关函数:\n")
        f.write("-"*50 + "\n")
        s.print_stats('write_videofile')
        
        # 查看 FFmpeg 相关
        f.write("\n\nFFmpeg 相关函数:\n")
        f.write("-"*50 + "\n")
        s.print_stats('ffmpeg')
        
        # 查看文件I/O相关
        f.write("\n\n文件I/O相关函数:\n")
        f.write("-"*50 + "\n")
        s.print_stats('write.*file|read.*file|open')
        
        # 查看进程相关（FFmpeg subprocess）
        f.write("\n\n进程/子进程相关函数:\n")
        f.write("-"*50 + "\n")
        s.print_stats('subprocess|Popen|communicate')
    
    print(f"✅ 详细分析报告保存到: {report_file}")
    
    # 在控制台显示关键信息
    print("\n" + "="*50)
    print("关键性能数据")
    print("="*50)
    
    # 创建一个字符串缓冲区来捕获输出
    string_buffer = io.StringIO()
    s = pstats.Stats(profiler, stream=string_buffer)
    
    # 显示累积时间最长的10个函数
    s.sort_stats('cumulative')
    s.print_stats(10)
    
    # 获取输出并显示
    output = string_buffer.getvalue()
    print("累积时间最长的10个函数:")
    print(output)
    
    # 分析 write_videofile 的调用
    print("\n专门查看 write_videofile:")
    string_buffer = io.StringIO()
    s = pstats.Stats(profiler, stream=string_buffer)
    s.print_stats('write_videofile')
    output = string_buffer.getvalue()
    print(output)
    
    return True

def analyze_results():
    """分析结果并提供优化建议"""
    
    output_dir = Path("performance_analysis")
    report_file = output_dir / "write_videofile_analysis.txt"
    
    if not report_file.exists():
        print("❌ 分析报告文件不存在")
        return
    
    print("\n" + "="*50)
    print("性能优化建议")
    print("="*50)
    
    # 读取报告内容
    with open(report_file, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # 基于分析结果提供建议
    suggestions = []
    
    if 'write_videofile' in content:
        suggestions.append("1. write_videofile 是主要瓶颈，考虑:")
        suggestions.append("   - 减少视频片段数量")
        suggestions.append("   - 使用更快的编码器")
        suggestions.append("   - 调整编码参数")
    
    if 'subprocess' in content or 'Popen' in content:
        suggestions.append("2. FFmpeg 子进程调用可能是瓶颈:")
        suggestions.append("   - 检查 FFmpeg 参数优化")
        suggestions.append("   - 考虑使用硬件加速")
    
    if 'read' in content or 'write' in content:
        suggestions.append("3. 文件I/O可能是瓶颈:")
        suggestions.append("   - 使用更快的存储设备")
        suggestions.append("   - 减少临时文件使用")
    
    suggestions.extend([
        "4. 通用优化建议:",
        "   - 减少视频分辨率进行测试",
        "   - 使用更激进的压缩设置",
        "   - 检查内存使用情况",
        "   - 考虑分段处理大文件"
    ])
    
    for suggestion in suggestions:
        print(suggestion)
    
    print(f"\n详细分析请查看: {report_file}")

if __name__ == "__main__":
    print("write_videofile 专门性能分析工具")
    print("="*50)
    print("这将使用 cProfile 对 write_videofile 进行详细的函数级分析")
    
    input("\n按 Enter 开始分析...")
    
    try:
        success = profile_write_videofile()
        if success:
            analyze_results()
    except Exception as e:
        print(f"❌ 分析失败: {e}")
        import traceback
        traceback.print_exc()
    
    print("\n分析完成!")
