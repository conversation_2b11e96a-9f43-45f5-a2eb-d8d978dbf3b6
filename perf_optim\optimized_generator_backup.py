"""
性能优化版本的视频生成器
通过减少视频层数和预渲染技术提升性能
"""

import os
import time
import numpy as np
from typing import List, Optional
from moviepy.editor import AudioFileClip, ImageClip, CompositeVideoClip, ColorClip, VideoClip
from PIL import Image, ImageDraw, ImageFont, ImageFilter, ImageEnhance
from pathlib import Path
import traceback

from lyric_timeline import LyricTimeline, LyricDisplayMode
from layout_types import LyricStyle
from lrc_mv_config import load_lrc_mv_config


class OptimizedJingwuGenerator:
    """性能优化版本的视频生成器（独立实现）"""
    
    def __init__(self, width: int = 720, height: int = 1280, fps: int = 24):
        self.width = width
        self.height = height
        self.fps = fps
        self.default_font_size = 80
        self.default_font_color = 'white'
        self.highlight_color = '#FFD700'  # 金色
        self.shadow_color = (0, 0, 0, 200)

        self.theme_colors = {
            'gold': '#FFD700',
            'red': '#DC143C',
            'dark_red': '#8B0000',
            'black': '#000000',
            'white': '#FFFFFF',
            'silver': '#C0C0C0'
        }

    def load_background_image(self, bg_path: str) -> Optional[np.ndarray]:
        """加载并处理背景图片"""
        try:
            img = Image.open(bg_path)
            # PIL版本兼容性处理
            try:
                img = img.resize((self.width, self.height), Image.Resampling.LANCZOS)
            except AttributeError:
                # 较旧的PIL版本回退
                img = img.resize((self.width, self.height))

            enhancer = ImageEnhance.Brightness(img)
            img = enhancer.enhance(0.4)
            enhancer = ImageEnhance.Contrast(img)
            img = enhancer.enhance(0.6)
            img = img.filter(ImageFilter.GaussianBlur(radius=1))
            return np.array(img)
        except Exception as e:
            print(f"⚠️  背景图片加载失败: {e}")
            return None

    def _create_video_background(self, duration: float, background_image_path: Optional[str] = None) -> ImageClip:
        """创建视频背景片段（图片或纯黑）"""
        if background_image_path and os.path.exists(background_image_path):
            bg_array = self.load_background_image(background_image_path)
            if bg_array is not None:
                print(f"   使用背景图片: {background_image_path}")
                return ImageClip(bg_array, duration=duration)
            else:
                print("   背景图片加载失败，使用纯黑背景替代。")
                return ColorClip(size=(self.width, self.height), color=(0,0,0), duration=duration)
        else:
            if background_image_path:
                print(f"   背景图片路径不存在: {background_image_path}。使用纯黑背景。")
            else:
                print("   未使用背景图片，使用纯黑背景。")
            return ColorClip(size=(self.width, self.height), color=(0,0,0), duration=duration)

    def _finalize_and_export_video(self, all_clips: List[ImageClip], audio_clip: AudioFileClip, 
                                 output_path: str, temp_audio_file_suffix: str = "generic",
                                 quality_profile: str = "draft"):
        """合成所有片段并导出视频"""
        print("合成视频...")
        final_video = CompositeVideoClip(all_clips)
        final_video = final_video.set_audio(audio_clip)
        final_video = final_video.set_fps(self.fps)

        temp_audio_filename = f'temp-audio-{temp_audio_file_suffix}-{hash(output_path) % 10000}.m4a'

        if quality_profile == "draft":
            print("   使用草稿质量配置进行编码...")
            codec_to_use = 'h264_nvenc'
            preset_to_use = 'fast'
            actual_ffmpeg_params = ['-cq', '28']
        else:
            print("   使用产品质量配置进行编码...")
            codec_to_use = 'libx264'
            preset_to_use = 'medium'
            actual_ffmpeg_params = ['-crf', '18']

        print(f"导出视频到: {output_path} (编码器: {codec_to_use}, 预设: {preset_to_use})")
        
        export_start_time = time.perf_counter()
        try:
            final_video.write_videofile(
                output_path,
                codec=codec_to_use,
                audio_codec='aac',
                temp_audiofile=temp_audio_filename,
                remove_temp=True,
                verbose=False,
                logger=None,
                preset=preset_to_use,
                ffmpeg_params=actual_ffmpeg_params
            )
        except Exception as e:
            if quality_profile == "draft" and codec_to_use == "h264_nvenc":
                print(f"警告：使用 {codec_to_use} 编码失败 ({e})。尝试使用 libx264 作为备选方案。")
                final_video.write_videofile(
                    output_path,
                    codec='libx264',
                    audio_codec='aac',
                    temp_audiofile=temp_audio_filename,
                    remove_temp=True,
                    verbose=False,
                    logger=None,
                    preset='ultrafast',
                    ffmpeg_params=['-crf', '28']
                )
            else:
                raise
        finally:
            export_end_time = time.perf_counter()
            print(f"视频导出操作总耗时: {export_end_time - export_start_time:.2f} 秒")
    
    def _render_lyric_text_to_image(self, 
                                   text: str, 
                                   width: int, 
                                   height: int, 
                                   scale_factor: float = 1.0,
                                   is_auxiliary: bool = False) -> Optional[Image.Image]:
        """将歌词文本渲染为图像"""
        try:
            if not text.strip():
                return None
            
            # 调整字体大小
            font_size = int(self.default_font_size * scale_factor)
            if is_auxiliary:
                font_size = int(font_size * 0.8)  # 副歌词稍小
            
            # 创建字体
            try:
                font = ImageFont.truetype("arial.ttf", font_size)
            except OSError:
                font = ImageFont.load_default()
            
            # 创建临时图像计算文本尺寸
            temp_img = Image.new('RGBA', (1, 1))
            temp_draw = ImageDraw.Draw(temp_img)
            text_bbox = temp_draw.textbbox((0, 0), text, font=font)
            text_width = text_bbox[2] - text_bbox[0]
            text_height = text_bbox[3] - text_bbox[1]
            
            # 创建歌词图像
            img = Image.new('RGBA', (width, height), (0, 0, 0, 0))
            draw = ImageDraw.Draw(img)
            
            # 计算位置
            x = (width - text_width) // 2
            y_offset = height // 3 if is_auxiliary else height // 2
            y = y_offset - text_height // 2
            
            # 绘制阴影
            shadow_offset = max(2, int(3 * scale_factor))
            draw.text((x + shadow_offset, y + shadow_offset), text, 
                     font=font, fill=self.shadow_color)
            
            # 绘制主文本
            text_color = self.default_font_color if not is_auxiliary else self.theme_colors['silver']
            draw.text((x, y), text, font=font, fill=text_color)
            
            return img
            
        except Exception as e:            print(f"渲染歌词图像时出错: {e}")
            return None

    def _create_optimized_lyric_layer(self, 
                                    main_timeline: LyricTimeline,
                                    aux_timeline: Optional[LyricTimeline] = None,
                                    duration: float = 30.0,
                                    scale_factor: float = 1.0) -> VideoClip:
        """
        性能优化版本：预渲染所有歌词为单一视频层
        通过减少video层数来大幅提升性能
        """
        print("   创建优化的歌词层（预渲染模式）...")
        
        # 计算帧数
        total_frames = int(duration * self.fps)
        print(f"   将预渲染 {total_frames} 帧 ({duration:.1f}秒)")
        
        # 调整尺寸（如果需要）
        target_width = int(self.width * scale_factor)
        target_height = int(self.height * scale_factor)
        
        # 预渲染所有帧到内存
        frames = []
        
        # 进度追踪
        progress_interval = max(1, total_frames // 20)  # 每5%显示进度
        
        for frame_idx in range(total_frames):
            current_time = frame_idx / self.fps
            
            # 显示进度
            if frame_idx % progress_interval == 0:
                progress = (frame_idx / total_frames) * 100
                print(f"   预渲染进度: {progress:.1f}% ({frame_idx}/{total_frames})")
            
            # 创建透明背景
            frame_img = Image.new('RGBA', (target_width, target_height), (0, 0, 0, 0))
            
            # 获取当前时间的主歌词文本
            current_lyrics = []
            for timestamp, text in main_timeline.lyrics_data:
                if timestamp <= current_time < timestamp + 3.0:  # 假设每句歌词持续3秒
                    current_lyrics.append(text)
            
            # 渲染主歌词
            for lyric_text in current_lyrics:
                lyric_img = self._render_lyric_text_to_image(
                    lyric_text, target_width, target_height, scale_factor
                )
                if lyric_img:
                    frame_img = Image.alpha_composite(frame_img, lyric_img)
            
            # 处理副歌词（双语模式）
            if aux_timeline:
                aux_lyrics = []
                for timestamp, text in aux_timeline.lyrics_data:
                    if timestamp <= current_time < timestamp + 3.0:
                        aux_lyrics.append(text)
                
                for lyric_text in aux_lyrics:
                    lyric_img = self._render_lyric_text_to_image(
                        lyric_text, target_width, target_height, scale_factor, is_auxiliary=True
                    )
                    if lyric_img:
                        frame_img = Image.alpha_composite(frame_img, lyric_img)
            
            # 转换为RGB（去除alpha通道）并添加到帧列表
            rgb_frame = Image.new('RGB', (target_width, target_height), (0, 0, 0))
            rgb_frame.paste(frame_img, mask=frame_img.split()[-1])
            frames.append(np.array(rgb_frame))
        
        print(f"   预渲染完成，生成了 {len(frames)} 帧")
        
        # 创建帧生成函数
        def make_frame(t):
            frame_idx = int(t * self.fps)
            if frame_idx >= len(frames):
                frame_idx = len(frames) - 1
            return frames[frame_idx]        # 创建视频片段 - 使用VideoClip而不是ImageClip
        lyric_clip = VideoClip(make_frame=make_frame, duration=duration)
        return lyric_clip

    def generate_bilingual_video_optimized(self,
                                          main_timeline: LyricTimeline,
                                          aux_timeline: Optional[LyricTimeline] = None,
                                          audio_path: str = "",
                                          output_path: str = "",
                                          background_image: Optional[str] = None,
                                          t_max_sec: float = float('inf'),
                                          quality_profile: str = "draft",
                                          scale_factor: float = 1.0) -> bool:
        """
        性能优化版本的视频生成方法
        通过预渲染歌词和减少视频层数来提升性能
        
        Args:
            scale_factor: 分辨率缩放因子（0.5 = 半分辨率，提升速度）
        """
        is_bilingual_mode = aux_timeline is not None
        mode_name = f"优化版{'双语' if is_bilingual_mode else '增强'}"
        
        try:
            print(f"开始生成{mode_name}视频: {output_path}")
            
            # 音频处理
            print("加载音频...")
            audio = AudioFileClip(audio_path)
            original_duration = audio.duration
            duration = min(original_duration, t_max_sec)
            
            if duration < original_duration:
                audio = audio.subclip(0, duration)
                print(f"音频已截取至 {duration:.2f} 秒")
            
            print(f"音频时长: {duration:.2f} 秒")
            
            # 创建背景层
            print("创建背景层...")
            background_clip = self._create_video_background(duration, background_image)
            
            # 创建优化的歌词层（单一层，包含所有歌词）
            lyric_clip = self._create_optimized_lyric_layer(
                main_timeline, aux_timeline, duration, scale_factor
            )
            
            # 合成仅包含2层的视频
            print("合成视频层...")
            all_clips = [background_clip, lyric_clip]
            
            # 导出视频
            self._finalize_and_export_video(
                all_clips=all_clips,
                audio_clip=audio,
                output_path=output_path,
                temp_audio_file_suffix=f"optimized-{mode_name}",
                quality_profile=quality_profile
            )
            
            print(f"{mode_name}视频生成完成: {output_path}")
            return True
            
        except Exception as e:
            print(f"生成{mode_name}视频时发生错误: {e}")
            traceback.print_exc()
            return False


def demo_optimized_features(config_path: Path, 
                          t_max_sec: float = float('inf'), 
                          quality_profile: str = "draft",
                          scale_factor: float = 1.0):
    """使用优化版本生成器的演示函数"""
    print("精武英雄歌词视频生成器 - 性能优化版")
    print("=" * 50)

    try:
        # 加载配置文件
        print(f"加载配置文件: {config_path}")
        config = load_lrc_mv_config(str(config_path))          # 生成输出文件名
        base_output_path = config.get_output_path()
        output_dir = base_output_path.parent
        output_filename = f"jingwu_optimized_{quality_profile}_scale{scale_factor}.mp4"
        output_path = output_dir / output_filename
        print(f"输出路径: {output_path}")
        
        # 音频文件路径
        audio_path = config.get_audio_path()
        if not audio_path.exists():
            print(f"❌ 音频文件不存在: {audio_path}")
            return False
            
        # 背景图片路径
        background_path = config.get_background_path()
        if background_path and not background_path.exists():
            print(f"⚠️ 背景图片不存在，将使用纯黑背景: {background_path}")
            background_path = None
        
        # 创建优化版生成器
        generator = OptimizedJingwuGenerator(
            width=config.width, 
            height=config.height, 
            fps=24
        )
        
        # 创建主时间轴
        main_lrc_path = config.get_main_lrc_path()
        main_font_size = config.main_lrc.font_size or 80
        print(f"主歌词字体大小: {main_font_size}")
        
        main_style = LyricStyle(
            font_size=main_font_size,
            highlight_color='#FFD700',
            glow_enabled=True
        )
        
        main_timeline = LyricTimeline.from_lrc_file(
            str(main_lrc_path),
            language="chinese",
            display_mode=LyricDisplayMode.ENHANCED_PREVIEW,
            style=main_style
        )
        
        # 创建副时间轴（如果存在）
        aux_timeline = None
        if config.aux_lrc:
            aux_lrc_path = config.get_aux_lrc_path()
            if aux_lrc_path and aux_lrc_path.exists():
                aux_font_size = config.aux_lrc.font_size or 60
                print(f"副歌词字体大小: {aux_font_size}")
                
                aux_style = LyricStyle(
                    font_size=aux_font_size,
                    font_color='white'
                )
                
                aux_timeline = LyricTimeline.from_lrc_file(
                    str(aux_lrc_path),
                    language="english",
                    display_mode=LyricDisplayMode.SIMPLE_FADE,
                    style=aux_style
                )
        
        # 使用优化版本生成视频
        start_time = time.perf_counter()
        
        success = generator.generate_bilingual_video_optimized(
            main_timeline=main_timeline,
            aux_timeline=aux_timeline,
            audio_path=str(audio_path),
            output_path=str(output_path),
            background_image=str(background_path) if background_path else None,
            t_max_sec=t_max_sec,
            quality_profile=quality_profile,
            scale_factor=scale_factor
        )
        
        end_time = time.perf_counter()
        total_time = end_time - start_time
        
        if success:
            print("\n✅ 优化版视频生成成功！")
            print(f"输出文件: {output_path}")
            print(f"总耗时: {total_time:.2f} 秒")
            if output_path.exists():
                file_size = output_path.stat().st_size / (1024 * 1024)  # MB
                print(f"文件大小: {file_size:.1f} MB")
                
                # 计算处理速度
                video_duration = t_max_sec if t_max_sec != float('inf') else 30
                speed_ratio = video_duration / total_time
                print(f"处理速度: {speed_ratio:.2f}x 实时速度")
        else:
            print("\n❌ 优化版视频生成失败！")
        
        return success
        
    except Exception as e:
        print(f"演示过程中发生错误: {e}")
        traceback.print_exc()
        return False


if __name__ == "__main__":
    # 测试不同的性能配置
    config_path = Path(r"精武英雄\lrc-mv.yaml")
    
    print("🚀 开始性能优化测试")
    print("=" * 60)
    
    # 测试1: 草稿质量 + 半分辨率（最快）
    print("\n📊 测试1: 草稿质量 + 半分辨率")
    demo_optimized_features(
        config_path, 
        t_max_sec=15.0, 
        quality_profile="draft",
        scale_factor=0.5
    )
    
    # 测试2: 草稿质量 + 全分辨率
    print("\n📊 测试2: 草稿质量 + 全分辨率")
    demo_optimized_features(
        config_path, 
        t_max_sec=15.0, 
        quality_profile="draft",
        scale_factor=1.0
    )
    
    # 测试3: 生产质量 + 全分辨率（对比基准）
    print("\n📊 测试3: 生产质量 + 全分辨率")
    demo_optimized_features(
        config_path, 
        t_max_sec=15.0, 
        quality_profile="production",
        scale_factor=1.0
    )
    
    print("\n🎉 性能优化测试完成！")
