"""
简化的 py-spy 分析脚本 - 专门分析 write_videofile 性能
"""
import subprocess
import sys
import time
from pathlib import Path

def main():
    print("py-spy 性能分析工具")
    print("="*50)
    
    # 检查 py-spy
    try:
        result = subprocess.run(["py-spy", "--version"], capture_output=True, text=True, check=True)
        print(f"✅ py-spy 可用: {result.stdout.strip()}")
    except Exception:
        print("❌ py-spy 不可用，请先安装: pip install py-spy")
        return
    
    print("\\n创建目标脚本...")
    
    # 创建目标脚本
    target_script = '''
import sys
import os
import time
from pathlib import Path

sys.path.insert(0, str(Path(__file__).parent))

from enhanced_generator import demo_enhanced_features

print(f"目标进程 PID: {os.getpid()}")
print("等待 py-spy 附加...")
time.sleep(5)

print("开始执行...")
config_path = Path(r"精武英雄\\\\lrc-mv.yaml")

success = demo_enhanced_features(
    config_path=config_path,
    t_max_sec=20.0,
    quality_profile="draft"
)

print(f"完成: {success}")
'''
    
    script_path = Path("target_for_pyspy.py")
    with open(script_path, 'w', encoding='utf-8') as f:
        f.write(target_script)
    
    # 创建输出目录
    output_dir = Path("performance_analysis")
    output_dir.mkdir(exist_ok=True)
    
    print("\\n请按以下步骤操作:")
    print("1. 在一个新的终端窗口中运行:")
    print(f"   python {script_path}")
    print("\\n2. 当看到 PID 后，在另一个终端中运行以下命令之一:")
    print("\\n   实时查看:")
    print("   py-spy top --pid <PID>")
    print("\\n   生成火焰图:")
    flamegraph_path = output_dir / "flamegraph.svg"
    print(f"   py-spy record -o {flamegraph_path} --pid <PID>")
    print("\\n   生成调用图:")
    callgraph_path = output_dir / "callgraph.svg"
    print(f"   py-spy record -o {callgraph_path} --format speedscope --pid <PID>")
    
    print("\\n或者，让我为你自动执行...")
    choice = input("自动执行分析? (y/n): ").lower()
    
    if choice == 'y':
        print("\\n启动目标进程...")
        
        try:
            # 启动目标进程
            target_process = subprocess.Popen([
                sys.executable, str(script_path)
            ], stdout=subprocess.PIPE, stderr=subprocess.STDOUT, text=True)
            
            time.sleep(3)  # 等待进程启动
            
            if target_process.poll() is None:
                pid = target_process.pid
                print(f"目标 PID: {pid}")
                
                # 启动火焰图记录
                flamegraph_path = output_dir / "flamegraph.svg"
                print(f"开始记录火焰图到: {flamegraph_path}")
                
                spy_cmd = [
                    "py-spy", "record",
                    "--pid", str(pid),
                    "--output", str(flamegraph_path),
                    "--rate", "100",
                    "--duration", "120"  # 2分钟
                ]
                
                spy_process = subprocess.Popen(spy_cmd, stdout=subprocess.PIPE, stderr=subprocess.PIPE, text=True)
                
                # 等待目标完成
                target_output, _ = target_process.communicate()
                print("\\n目标进程输出:")
                print(target_output)
                
                # 等待 py-spy 完成
                spy_output, spy_error = spy_process.communicate()
                
                if spy_process.returncode == 0:
                    print(f"\\n✅ 火焰图生成成功: {flamegraph_path}")
                    
                    if flamegraph_path.exists():
                        size_kb = flamegraph_path.stat().st_size / 1024
                        print(f"   文件大小: {size_kb:.1f} KB")
                        print("   在浏览器中打开此文件查看详细分析")
                    else:
                        print("❌ 火焰图文件未找到")
                else:
                    print(f"❌ py-spy 失败: {spy_error}")
                    
            else:
                print("❌ 目标进程启动失败")
                
        except Exception as e:
            print(f"❌ 自动执行失败: {e}")
        finally:
            # 清理
            if script_path.exists():
                script_path.unlink()
    
    print("\\n分析完成!")
    
    if output_dir.exists():
        print("\\n生成的文件:")
        for file_path in output_dir.iterdir():
            if file_path.is_file():
                size_kb = file_path.stat().st_size / 1024
                print(f"  - {file_path.name} ({size_kb:.1f} KB)")

if __name__ == "__main__":
    main()
