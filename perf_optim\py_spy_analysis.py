"""
使用 py-spy 深度分析 write_videofile 性能的脚本
"""
import subprocess
import sys
import time
import threading
from pathlib import Path

def create_target_script():
    """创建用于 py-spy 分析的目标脚本"""
    script_content = '''
import sys
import time
from pathlib import Path

# 添加当前目录到路径
sys.path.insert(0, str(Path(__file__).parent))

from enhanced_generator import demo_enhanced_features

if __name__ == "__main__":
    print(f"目标脚本启动，PID: {os.getpid()}")
    print("开始执行 write_videofile 性能分析...")
    
    # 暂停一下让 py-spy 有时间附加
    time.sleep(3)
    
    config_path = Path(r"精武英雄\\lrc-mv.yaml")
    
    # 执行相对较短的测试
    success = demo_enhanced_features(
        config_path=config_path,
        t_max_sec=15.0,  # 15秒视频
        quality_profile="draft"
    )
    
    print(f"分析目标完成，结果: {'成功' if success else '失败'}")
'''
    
    # 需要添加 os 导入
    script_content = script_content.replace(
        'import sys',
        'import sys\nimport os'
    )
    
    script_path = Path("py_spy_target.py")
    with open(script_path, 'w', encoding='utf-8') as f:
        f.write(script_content)
    
    return script_path

def run_py_spy_analysis():
    """运行 py-spy 分析"""
    print("启动 py-spy 深度性能分析...")
    print("="*50)
    
    # 创建输出目录
    output_dir = Path("performance_analysis")
    output_dir.mkdir(exist_ok=True)
    
    # 创建目标脚本
    target_script = create_target_script()
    
    try:
        print("1. 启动目标进程...")
        # 启动目标脚本
        target_process = subprocess.Popen([
            sys.executable, str(target_script)
        ], stdout=subprocess.PIPE, stderr=subprocess.STDOUT, text=True, bufsize=1)
        
        # 等待进程启动并获取 PID
        time.sleep(2)
        
        if target_process.poll() is None:  # 进程正在运行
            pid = target_process.pid
            print(f"目标进程 PID: {pid}")
            
            # 生成火焰图
            flamegraph_path = output_dir / "write_videofile_flamegraph.svg"
            print(f"2. 开始生成火焰图: {flamegraph_path}")
            
            try:
                # 启动 py-spy 记录
                spy_cmd = [
                    "py-spy", "record",
                    "--pid", str(pid),
                    "--output", str(flamegraph_path),
                    "--format", "flamegraph",
                    "--rate", "100",  # 100Hz 采样率
                    "--duration", "180"  # 最多3分钟
                ]
                
                print("开始 py-spy 记录...")
                spy_process = subprocess.Popen(spy_cmd, stdout=subprocess.PIPE, stderr=subprocess.PIPE, text=True)
                
                # 实时输出目标进程的日志
                print("3. 目标进程输出:")
                print("-" * 30)
                
                def print_output():
                    for line in iter(target_process.stdout.readline, ''):
                        if line:
                            print(line.rstrip())
                
                output_thread = threading.Thread(target=print_output)
                output_thread.daemon = True
                output_thread.start()
                
                # 等待目标进程完成
                target_return_code = target_process.wait()
                
                # 等待 py-spy 完成
                spy_stdout, spy_stderr = spy_process.communicate(timeout=30)
                
                print("-" * 30)
                print(f"4. 分析完成!")
                print(f"目标进程返回码: {target_return_code}")
                
                if spy_process.returncode == 0:
                    print(f"✅ 火焰图生成成功: {flamegraph_path}")
                    
                    # 检查文件大小
                    if flamegraph_path.exists():
                        size_kb = flamegraph_path.stat().st_size / 1024
                        print(f"   文件大小: {size_kb:.1f} KB")
                        
                        # 生成文本报告
                        text_report_path = output_dir / "py_spy_report.txt"
                        with open(text_report_path, 'w', encoding='utf-8') as f:
                            f.write("py-spy 性能分析报告\\n")
                            f.write("="*50 + "\\n")
                            f.write(f"目标进程 PID: {pid}\\n")
                            f.write(f"火焰图路径: {flamegraph_path}\\n")
                            f.write(f"分析时间: {time.strftime('%Y-%m-%d %H:%M:%S')}\\n")
                            f.write("\\npy-spy 输出:\\n")
                            f.write(spy_stdout)
                            if spy_stderr:
                                f.write("\\n错误输出:\\n")
                                f.write(spy_stderr)
                        
                        print(f"✅ 文本报告保存到: {text_report_path}")
                        
                    else:
                        print("❌ 火焰图文件未生成")
                else:
                    print(f"❌ py-spy 失败: {spy_stderr}")
                    
            except subprocess.TimeoutExpired:
                print("⚠️ py-spy 超时")
                spy_process.kill()
            except Exception as e:
                print(f"❌ py-spy 执行失败: {e}")
                
        else:
            print("❌ 目标进程启动失败")
            stdout, stderr = target_process.communicate()
            print(f"输出: {stdout}")
            print(f"错误: {stderr}")
            
    except Exception as e:
        print(f"❌ 分析过程失败: {e}")
    finally:
        # 清理
        if target_script.exists():
            target_script.unlink()
            
        # 确保进程被终止
        try:
            if 'target_process' in locals() and target_process.poll() is None:
                target_process.terminate()
                target_process.wait(timeout=5)
        except:
            pass

def analyze_results():
    """分析结果并提供建议"""
    output_dir = Path("performance_analysis")
    
    if not output_dir.exists():
        print("❌ 性能分析目录不存在")
        return
    
    print("\\n" + "="*50)
    print("结果分析")
    print("="*50)
    
    flamegraph_path = output_dir / "write_videofile_flamegraph.svg"
    report_path = output_dir / "py_spy_report.txt"
    
    if flamegraph_path.exists():
        print(f"✅ 火焰图: {flamegraph_path}")
        print("   在浏览器中打开此文件可查看详细的性能分析")
        print("   红色区域表示 CPU 使用率高的函数")
        
    if report_path.exists():
        print(f"✅ 文本报告: {report_path}")
    
    print("\\n性能优化建议:")
    print("1. 查看火焰图中 write_videofile 的调用栈")
    print("2. 检查是否有 FFmpeg 相关的瓶颈")
    print("3. 观察 Python GIL 对性能的影响")
    print("4. 寻找可能的内存分配瓶颈")
    
    # 基于前面的分析给出具体建议
    print("\\n根据初步分析的建议:")
    print("- write_videofile 占用 90%+ 的处理时间")
    print("- 考虑减少视频片段数量")
    print("- 检查 temp_audiofile 处理是否可以优化")
    print("- 尝试不同的 FFmpeg 参数组合")

if __name__ == "__main__":
    print("py-spy 深度性能分析工具")
    print("="*50)
    
    # 检查 py-spy 是否可用
    try:
        result = subprocess.run(["py-spy", "--version"], capture_output=True, text=True, check=True)
        print(f"✅ py-spy 版本: {result.stdout.strip()}")
    except (subprocess.CalledProcessError, FileNotFoundError):
        print("❌ py-spy 不可用，请先安装: pip install py-spy")
        sys.exit(1)
    
    print("\\n这将启动一个目标进程并使用 py-spy 进行性能分析")
    input("按 Enter 继续...")
    
    run_py_spy_analysis()
    analyze_results()
    
    print("\\n分析完成!")
