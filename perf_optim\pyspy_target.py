"""
简单的性能测试目标脚本
"""
import sys
import os
import time
from pathlib import Path

sys.path.insert(0, str(Path(__file__).parent))

print(f"目标进程 PID: {os.getpid()}")
print("等待 5 秒让 py-spy 附加...")
time.sleep(5)

print("开始执行性能测试...")

from enhanced_generator import demo_enhanced_features

config_path = Path(r"精武英雄\lrc-mv.yaml")

# 执行一个相对快速的测试
success = demo_enhanced_features(
    config_path=config_path,
    t_max_sec=15.0,  # 15秒视频
    quality_profile="draft"
)

print(f"测试完成，结果: {success}")
time.sleep(2)  # 等待一下确保 py-spy 能捕获到结束
