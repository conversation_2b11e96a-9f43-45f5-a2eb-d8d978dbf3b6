"""
性能分析脚本 - 使用 py-spy 分析 write_videofile 性能
"""
import subprocess
import sys
import time
import threading
from pathlib import Path
import psutil
import os

def install_py_spy():
    """安装 py-spy"""
    print("安装 py-spy...")
    try:
        subprocess.run([sys.executable, "-m", "pip", "install", "py-spy"], 
                      check=True, capture_output=True, text=True)
        print("✅ py-spy 安装成功")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ py-spy 安装失败: {e}")
        return False

def start_py_spy_recording(target_script: str, output_dir: Path):
    """启动 py-spy 记录"""
    output_dir.mkdir(exist_ok=True)
    
    # 生成火焰图
    flame_graph_path = output_dir / "flamegraph.svg"
    raw_data_path = output_dir / "raw_profile.txt"
    
    print(f"开始 py-spy 性能分析...")
    print(f"目标脚本: {target_script}")
    print(f"火焰图输出: {flame_graph_path}")
    print(f"原始数据: {raw_data_path}")
    
    # 启动目标脚本
    cmd = [sys.executable, target_script]
    process = subprocess.Popen(cmd, stdout=subprocess.PIPE, stderr=subprocess.PIPE, text=True)
    
    # 等待进程启动
    time.sleep(2)
    
    if process.poll() is None:  # 进程还在运行
        pid = process.pid
        print(f"目标进程 PID: {pid}")
        
        # 启动 py-spy 记录火焰图
        spy_cmd = [
            "py-spy", "record", 
            "--pid", str(pid),
            "--output", str(flame_graph_path),
            "--format", "flamegraph",
            "--rate", "100",  # 100Hz 采样率
            "--duration", "300"  # 最多记录5分钟
        ]
        
        # 启动 py-spy 顶部视图（在另一个线程中）
        def run_top_view():
            try:
                top_cmd = ["py-spy", "top", "--pid", str(pid)]
                subprocess.run(top_cmd, timeout=300)
            except (subprocess.TimeoutExpired, subprocess.CalledProcessError):
                pass
        
        top_thread = threading.Thread(target=run_top_view)
        top_thread.daemon = True
        top_thread.start()
        
        try:
            print("开始 py-spy 记录（火焰图）...")
            spy_process = subprocess.run(spy_cmd, capture_output=True, text=True, timeout=300)
            
            if spy_process.returncode == 0:
                print(f"✅ 火焰图生成成功: {flame_graph_path}")
            else:
                print(f"❌ py-spy 记录失败: {spy_process.stderr}")
                
        except subprocess.TimeoutExpired:
            print("⚠️ py-spy 记录超时")
        
        # 等待目标进程完成
        stdout, stderr = process.communicate()
        print("目标脚本输出:")
        print(stdout)
        if stderr:
            print("错误输出:")
            print(stderr)
            
    else:
        print("❌ 目标进程启动失败")
        stdout, stderr = process.communicate()
        print(f"输出: {stdout}")
        print(f"错误: {stderr}")

def create_profile_target_script():
    """创建专门用于性能分析的目标脚本"""
    script_content = '''
import sys
import os
from pathlib import Path

# 添加当前目录到 Python 路径
current_dir = Path(__file__).parent
sys.path.insert(0, str(current_dir))

from enhanced_generator import demo_enhanced_features

if __name__ == "__main__":
    print("开始性能分析目标脚本...")
    
    # 使用较短的时长以加快分析
    config_path = Path(r"精武英雄\\lrc-mv.yaml")
    
    # 使用 draft 质量以突出 write_videofile 的性能问题
    success = demo_enhanced_features(
        config_path=config_path, 
        t_max_sec=20.0,  # 只生成20秒的视频
        quality_profile="draft"
    )
    
    if success:
        print("✅ 性能分析目标脚本执行成功")
    else:
        print("❌ 性能分析目标脚本执行失败")
'''
    
    script_path = Path("profile_target.py")
    with open(script_path, 'w', encoding='utf-8') as f:
        f.write(script_content)
    
    return script_path

def analyze_with_cprofile():
    """使用 cProfile 进行补充分析"""
    print("\\n" + "="*50)
    print("使用 cProfile 进行补充分析")
    print("="*50)
    
    script_content = '''
import cProfile
import pstats
import sys
from pathlib import Path

# 添加当前目录到 Python 路径
current_dir = Path(__file__).parent
sys.path.insert(0, str(current_dir))

from enhanced_generator import demo_enhanced_features

def run_profile():
    config_path = Path(r"精武英雄\\lrc-mv.yaml")
    return demo_enhanced_features(
        config_path=config_path, 
        t_max_sec=15.0,  # 较短时长
        quality_profile="draft"
    )

if __name__ == "__main__":
    pr = cProfile.Profile()
    pr.enable()
    
    result = run_profile()
    
    pr.disable()
    
    # 保存详细的性能数据
    pr.dump_stats('performance_analysis/cprofile_results.prof')
    
    # 生成文本报告
    with open('performance_analysis/cprofile_report.txt', 'w') as f:
        ps = pstats.Stats(pr, stream=f)
        ps.sort_stats('cumulative')
        ps.print_stats(50)  # 显示前50个最耗时的函数
        
        f.write("\\n" + "="*50 + "\\n")
        f.write("按总时间排序:\\n")
        f.write("="*50 + "\\n")
        ps.sort_stats('tottime')
        ps.print_stats(30)
        
        f.write("\\n" + "="*50 + "\\n") 
        f.write("write_videofile 相关函数:\\n")
        f.write("="*50 + "\\n")
        ps.print_stats('write_videofile')
        ps.print_stats('moviepy')
        ps.print_stats('ffmpeg')
    
    print("✅ cProfile 分析完成，结果保存到 performance_analysis/")
'''
    
    script_path = Path("cprofile_analysis.py")
    with open(script_path, 'w', encoding='utf-8') as f:
        f.write(script_content)
    
    # 运行 cProfile 分析
    try:
        subprocess.run([sys.executable, str(script_path)], check=True)
        print("✅ cProfile 分析完成")
    except subprocess.CalledProcessError as e:
        print(f"❌ cProfile 分析失败: {e}")

def main():
    """主函数"""
    print("MoviePy write_videofile 性能分析工具")
    print("="*50)
    
    # 检查 py-spy 是否已安装
    try:
        subprocess.run(["py-spy", "--version"], capture_output=True, check=True)
        print("✅ py-spy 已安装")
    except (subprocess.CalledProcessError, FileNotFoundError):
        print("❌ py-spy 未安装，正在安装...")
        if not install_py_spy():
            print("无法安装 py-spy，将只使用 cProfile 分析")
            analyze_with_cprofile()
            return
    
    # 创建输出目录
    output_dir = Path("performance_analysis")
    output_dir.mkdir(exist_ok=True)
    
    print("\\n选择分析方法:")
    print("1. py-spy 火焰图分析 (推荐 - 可分析 C 扩展)")
    print("2. cProfile 分析 (详细的函数调用统计)")
    print("3. 两种方法都使用 (最全面)")
    
    choice = input("请选择 (1/2/3): ").strip()
    
    if choice in ['1', '3']:
        print("\\n开始 py-spy 分析...")
        target_script = create_profile_target_script()
        start_py_spy_recording(str(target_script), output_dir)
        
        # 清理临时脚本
        target_script.unlink(missing_ok=True)
    
    if choice in ['2', '3']:
        analyze_with_cprofile()
    
    print("\\n" + "="*50)
    print("性能分析完成！")
    print("结果文件:")
    
    if output_dir.exists():
        for file_path in output_dir.iterdir():
            if file_path.is_file():
                size_mb = file_path.stat().st_size / 1024 / 1024
                print(f"  - {file_path.name} ({size_mb:.2f} MB)")
    
    print("\\n建议:")
    print("1. 查看火焰图 (flamegraph.svg) 了解整体性能瓶颈")
    print("2. 查看 cProfile 报告了解具体函数耗时")
    print("3. 关注 write_videofile 和 FFmpeg 相关的函数调用")

if __name__ == "__main__":
    main()
