MoviePy 2.1.2 性能分析报告
==================================================

完整统计信息:
         436642 function calls (413139 primitive calls) in 39.398 seconds

   Ordered by: cumulative time
   List reduced from 1162 to 20 due to restriction <20>

   ncalls  tottime  percall  cumtime  percall filename:lineno(function)
        1    0.000    0.000   39.340   39.340 E:\repos\jingwu-hero\enhanced_generator.py:447(demo_enhanced_features)
        1    0.003    0.003   39.328   39.328 E:\repos\jingwu-hero\enhanced_generator.py:309(generate_bilingual_video)
        1    0.003    0.003   39.324   39.324 E:\repos\jingwu-hero\enhanced_generator.py:334(_generate_video_with_timelines)
  9349/35    0.057    0.000   36.125    1.032 E:\Miniconda3\envs\lyrc-mv\Lib\site-packages\moviepy\decorators.py:89(wrapper)
        1    0.000    0.000   36.016   36.016 E:\repos\jingwu-hero\enhanced_generator.py:278(_finalize_and_export_video)
        1    0.000    0.000   35.991   35.991 <decorator-gen-62>:1(write_videofile)
    226/1    0.001    0.000   35.991   35.991 E:\Miniconda3\envs\lyrc-mv\Lib\site-packages\moviepy\decorators.py:47(requires_duration)
        1    0.000    0.000   35.991   35.991 <decorator-gen-61>:1(write_videofile)
      2/1    0.000    0.000   35.991   35.991 E:\Miniconda3\envs\lyrc-mv\Lib\site-packages\moviepy\decorators.py:143(wrapper)
        1    0.000    0.000   35.991   35.991 <decorator-gen-60>:1(write_videofile)
        1    0.000    0.000   35.991   35.991 E:\Miniconda3\envs\lyrc-mv\Lib\site-packages\moviepy\decorators.py:19(convert_masks_to_RGB)
        1    0.000    0.000   35.991   35.991 <decorator-gen-59>:1(write_videofile)
        1    0.000    0.000   35.991   35.991 E:\Miniconda3\envs\lyrc-mv\Lib\site-packages\moviepy\video\VideoClip.py:204(write_videofile)
        1    0.793    0.793   35.581   35.581 E:\Miniconda3\envs\lyrc-mv\Lib\site-packages\moviepy\video\io\ffmpeg_writer.py:242(ffmpeg_write_video)
 6443/711    0.010    0.000   32.362    0.046 <decorator-gen-17>:1(get_frame)
 6443/711    0.189    0.000   32.348    0.045 E:\Miniconda3\envs\lyrc-mv\Lib\site-packages\moviepy\Clip.py:66(get_frame)
      480    1.797    0.004   32.064    0.067 E:\Miniconda3\envs\lyrc-mv\Lib\site-packages\moviepy\video\compositing\CompositeVideoClip.py:130(frame_function)
      241    0.051    0.000   21.518    0.089 E:\Miniconda3\envs\lyrc-mv\Lib\site-packages\moviepy\Clip.py:510(iter_frames)
      888    2.002    0.002   15.583    0.018 E:\Miniconda3\envs\lyrc-mv\Lib\site-packages\moviepy\video\VideoClip.py:720(compose_on)
      888    8.325    0.009   10.418    0.012 E:\Miniconda3\envs\lyrc-mv\Lib\site-packages\moviepy\video\VideoClip.py:796(compose_mask)




PIL相关:
         436642 function calls (413139 primitive calls) in 39.398 seconds

   Ordered by: cumulative time
   List reduced from 1162 to 171 due to restriction <'PIL'>

   ncalls  tottime  percall  cumtime  percall filename:lineno(function)
      888    0.006    0.000    2.973    0.003 E:\Miniconda3\envs\lyrc-mv\Lib\site-packages\PIL\Image.py:3579(alpha_composite)
     2028    0.013    0.000    2.958    0.001 E:\Miniconda3\envs\lyrc-mv\Lib\site-packages\PIL\Image.py:927(convert)
      888    2.942    0.003    2.942    0.003 {built-in method PIL._imaging.alpha_composite}
     1282    0.006    0.000    2.545    0.002 E:\Miniconda3\envs\lyrc-mv\Lib\site-packages\PIL\ImageDraw.py:557(text)
     1282    0.014    0.000    2.532    0.002 E:\Miniconda3\envs\lyrc-mv\Lib\site-packages\PIL\ImageDraw.py:612(draw_text)
     2016    0.060    0.000    2.005    0.001 E:\Miniconda3\envs\lyrc-mv\Lib\site-packages\PIL\Image.py:3241(fromarray)
     2911    0.021    0.000    1.977    0.001 E:\Miniconda3\envs\lyrc-mv\Lib\site-packages\PIL\Image.py:3059(new)
     2016    0.012    0.000    1.941    0.001 E:\Miniconda3\envs\lyrc-mv\Lib\site-packages\PIL\Image.py:3157(frombuffer)
     4193    1.926    0.000    1.926    0.000 {built-in method PIL._imaging.fill}
     1128    0.010    0.000    1.887    0.002 E:\Miniconda3\envs\lyrc-mv\Lib\site-packages\PIL\Image.py:3111(frombytes)
     1282    0.003    0.000    1.001    0.001 E:\Miniconda3\envs\lyrc-mv\Lib\site-packages\PIL\ImageFont.py:549(getmask2)
     1128    0.008    0.000    0.781    0.001 E:\Miniconda3\envs\lyrc-mv\Lib\site-packages\PIL\Image.py:843(frombytes)
      246    0.072    0.000    0.768    0.003 E:\Miniconda3\envs\lyrc-mv\Lib\site-packages\PIL\Image.py:726(__array_interface__)
      246    0.015    0.000    0.693    0.003 E:\Miniconda3\envs\lyrc-mv\Lib\site-packages\PIL\Image.py:764(tobytes)
      888    0.005    0.000    0.517    0.001 E:\Miniconda3\envs\lyrc-mv\Lib\site-packages\PIL\Image.py:1935(putalpha)
     11/6    0.004    0.000    0.440    0.073 E:\Miniconda3\envs\lyrc-mv\Lib\site-packages\PIL\Image.py:2210(resize)
      889    0.002    0.000    0.438    0.000 E:\Miniconda3\envs\lyrc-mv\Lib\site-packages\PIL\Image.py:1263(copy)
      888    0.008    0.000    0.361    0.000 E:\Miniconda3\envs\lyrc-mv\Lib\site-packages\PIL\Image.py:1728(paste)
     6724    0.047    0.000    0.066    0.000 E:\Miniconda3\envs\lyrc-mv\Lib\site-packages\PIL\Image.py:588(_new)
        1    0.000    0.000    0.039    0.039 E:\Miniconda3\envs\lyrc-mv\Lib\site-packages\PIL\Image.py:1355(filter)
        1    0.000    0.000    0.039    0.039 E:\Miniconda3\envs\lyrc-mv\Lib\site-packages\PIL\ImageFilter.py:187(filter)
     8508    0.014    0.000    0.035    0.000 E:\Miniconda3\envs\lyrc-mv\Lib\site-packages\PIL\Image.py:880(load)
        1    0.000    0.000    0.023    0.023 E:\Miniconda3\envs\lyrc-mv\Lib\site-packages\PIL\Image.py:3449(open)
        1    0.000    0.000    0.022    0.022 E:\Miniconda3\envs\lyrc-mv\Lib\site-packages\PIL\Image.py:338(preinit)
     9636    0.018    0.000    0.021    0.000 E:\Miniconda3\envs\lyrc-mv\Lib\site-packages\PIL\Image.py:543(__init__)
     1129    0.011    0.000    0.019    0.000 E:\Miniconda3\envs\lyrc-mv\Lib\site-packages\PIL\Image.py:414(_getdecoder)
        5    0.000    0.000    0.018    0.004 E:\Miniconda3\envs\lyrc-mv\Lib\site-packages\PIL\ImageFont.py:803(truetype)
    17256    0.010    0.000    0.016    0.000 E:\Miniconda3\envs\lyrc-mv\Lib\site-packages\PIL\Image.py:553(im)
     1282    0.002    0.000    0.015    0.000 E:\Miniconda3\envs\lyrc-mv\Lib\site-packages\PIL\ImageFont.py:635(fill)
     6055    0.007    0.000    0.013    0.000 E:\Miniconda3\envs\lyrc-mv\Lib\site-packages\PIL\Image.py:3040(_check_size)
     1776    0.002    0.000    0.011    0.000 E:\Miniconda3\envs\lyrc-mv\Lib\site-packages\PIL\Image.py:640(_ensure_mutable)
       10    0.000    0.000    0.011    0.001 E:\Miniconda3\envs\lyrc-mv\Lib\site-packages\PIL\ImageFont.py:876(freetype)
       10    0.000    0.000    0.011    0.001 E:\Miniconda3\envs\lyrc-mv\Lib\site-packages\PIL\ImageFont.py:217(__init__)
        2    0.000    0.000    0.010    0.005 E:\Miniconda3\envs\lyrc-mv\Lib\site-packages\PIL\ImageEnhance.py:29(enhance)
        2    0.000    0.000    0.010    0.005 E:\Miniconda3\envs\lyrc-mv\Lib\site-packages\PIL\Image.py:3594(blend)
        2    0.010    0.005    0.010    0.005 {built-in method PIL._imaging.blend}
    10288    0.008    0.000    0.008    0.000 E:\Miniconda3\envs\lyrc-mv\Lib\site-packages\PIL\Image.py:580(readonly)
      888    0.007    0.000    0.007    0.000 {built-in method PIL._imaging.map_buffer}
        1    0.000    0.000    0.007    0.007 E:\Miniconda3\envs\lyrc-mv\Lib\site-packages\PIL\ImageEnhance.py:71(__init__)
        1    0.000    0.000    0.006    0.006 E:\Miniconda3\envs\lyrc-mv\Lib\site-packages\PIL\GifImagePlugin.py:1(<module>)
        1    0.000    0.000    0.006    0.006 E:\Miniconda3\envs\lyrc-mv\Lib\site-packages\PIL\BmpImagePlugin.py:1(<module>)
        1    0.000    0.000    0.006    0.006 E:\Miniconda3\envs\lyrc-mv\Lib\site-packages\PIL\ImageFile.py:272(load)
     1282    0.001    0.000    0.005    0.000 E:\Miniconda3\envs\lyrc-mv\Lib\site-packages\PIL\ImageDraw.py:605(getink)
     1283    0.002    0.000    0.004    0.000 E:\Miniconda3\envs\lyrc-mv\Lib\site-packages\PIL\Image.py:3428(_decompression_bomb_check)
      246    0.003    0.000    0.004    0.000 E:\Miniconda3\envs\lyrc-mv\Lib\site-packages\PIL\Image.py:439(_getencoder)
     1282    0.002    0.000    0.004    0.000 E:\Miniconda3\envs\lyrc-mv\Lib\site-packages\PIL\ImageDraw.py:145(_getink)
     1128    0.004    0.000    0.004    0.000 {built-in method PIL._imaging.raw_decoder}
      246    0.003    0.000    0.004    0.000 E:\Miniconda3\envs\lyrc-mv\Lib\site-packages\PIL\Image.py:244(_conv_type_shape)
        1    0.000    0.000    0.003    0.003 E:\Miniconda3\envs\lyrc-mv\Lib\site-packages\PIL\ImagePalette.py:1(<module>)
     3636    0.002    0.000    0.003    0.000 E:\Miniconda3\envs\lyrc-mv\Lib\site-packages\PIL\Image.py:564(width)
        5    0.000    0.000    0.003    0.001 E:\Miniconda3\envs\lyrc-mv\Lib\site-packages\PIL\ImageDraw.py:879(textbbox)
        5    0.000    0.000    0.003    0.001 E:\Miniconda3\envs\lyrc-mv\Lib\site-packages\PIL\ImageFont.py:392(getbbox)
    10526    0.003    0.000    0.003    0.000 E:\Miniconda3\envs\lyrc-mv\Lib\site-packages\PIL\Image.py:584(readonly)
    13564    0.003    0.000    0.003    0.000 E:\Miniconda3\envs\lyrc-mv\Lib\site-packages\PIL\Image.py:576(mode)
     3636    0.002    0.000    0.002    0.000 E:\Miniconda3\envs\lyrc-mv\Lib\site-packages\PIL\Image.py:568(height)
        1    0.000    0.000    0.002    0.002 E:\Miniconda3\envs\lyrc-mv\Lib\site-packages\PIL\ImageStat.py:32(__init__)
        1    0.000    0.000    0.002    0.002 E:\Miniconda3\envs\lyrc-mv\Lib\site-packages\PIL\Image.py:1667(histogram)
    11982    0.002    0.000    0.002    0.000 E:\Miniconda3\envs\lyrc-mv\Lib\site-packages\PIL\Image.py:572(size)
        1    0.000    0.000    0.002    0.002 E:\Miniconda3\envs\lyrc-mv\Lib\site-packages\PIL\PngImagePlugin.py:1(<module>)
     1287    0.001    0.000    0.002    0.000 E:\Miniconda3\envs\lyrc-mv\Lib\site-packages\PIL\ImageDraw.py:552(_multiline_check)
       10    0.001    0.000    0.001    0.000 {built-in method PIL._imagingft.getfont}
     6725    0.001    0.000    0.001    0.000 E:\Miniconda3\envs\lyrc-mv\Lib\site-packages\PIL\Image.py:560(im)
        1    0.000    0.000    0.001    0.001 E:\Miniconda3\envs\lyrc-mv\Lib\site-packages\PIL\JpegImagePlugin.py:1(<module>)
     1287    0.001    0.000    0.001    0.000 E:\Miniconda3\envs\lyrc-mv\Lib\site-packages\PIL\ImageFont.py:72(_string_length_check)
        1    0.000    0.000    0.001    0.001 E:\Miniconda3\envs\lyrc-mv\Lib\site-packages\PIL\ImageEnhance.py:1(<module>)
        1    0.000    0.000    0.001    0.001 E:\Miniconda3\envs\lyrc-mv\Lib\site-packages\PIL\ImageMath.py:1(<module>)
        1    0.000    0.000    0.001    0.001 E:\Miniconda3\envs\lyrc-mv\Lib\site-packages\PIL\ImageFile.py:1(<module>)
        1    0.000    0.000    0.001    0.001 E:\Miniconda3\envs\lyrc-mv\Lib\site-packages\PIL\ImageEnhance.py:92(__init__)
      246    0.001    0.000    0.001    0.000 {built-in method PIL._imaging.raw_encoder}
        1    0.000    0.000    0.000    0.000 E:\Miniconda3\envs\lyrc-mv\Lib\site-packages\PIL\Image.py:3522(_open_core)
        1    0.000    0.000    0.000    0.000 E:\Miniconda3\envs\lyrc-mv\Lib\site-packages\PIL\ImageFile.py:116(__init__)
        4    0.000    0.000    0.000    0.000 E:\Miniconda3\envs\lyrc-mv\Lib\site-packages\PIL\PngImagePlugin.py:984(load_read)
        1    0.000    0.000    0.000    0.000 E:\Miniconda3\envs\lyrc-mv\Lib\site-packages\PIL\PngImagePlugin.py:756(_open)
        5    0.000    0.000    0.000    0.000 E:\Miniconda3\envs\lyrc-mv\Lib\site-packages\PIL\ImageDraw.py:994(Draw)
        1    0.000    0.000    0.000    0.000 E:\Miniconda3\envs\lyrc-mv\Lib\site-packages\PIL\ImageOps.py:1(<module>)
        5    0.000    0.000    0.000    0.000 E:\Miniconda3\envs\lyrc-mv\Lib\site-packages\PIL\ImageDraw.py:66(__init__)
        1    0.000    0.000    0.000    0.000 E:\Miniconda3\envs\lyrc-mv\Lib\site-packages\PIL\PpmImagePlugin.py:1(<module>)
        1    0.000    0.000    0.000    0.000 E:\Miniconda3\envs\lyrc-mv\Lib\site-packages\PIL\JpegImagePlugin.py:336(JpegImageFile)
        5    0.000    0.000    0.000    0.000 E:\Miniconda3\envs\lyrc-mv\Lib\site-packages\PIL\PngImagePlugin.py:197(call)
        1    0.000    0.000    0.000    0.000 E:\Miniconda3\envs\lyrc-mv\Lib\site-packages\PIL\ImageStat.py:120(mean)
        9    0.000    0.000    0.000    0.000 E:\Miniconda3\envs\lyrc-mv\Lib\site-packages\PIL\PngImagePlugin.py:163(read)
        1    0.000    0.000    0.000    0.000 E:\Miniconda3\envs\lyrc-mv\Lib\site-packages\PIL\GifImagePlugin.py:56(LoadingStrategy)
        1    0.000    0.000    0.000    0.000 E:\Miniconda3\envs\lyrc-mv\Lib\site-packages\PIL\ImageStat.py:96(sum)
        1    0.000    0.000    0.000    0.000 E:\Miniconda3\envs\lyrc-mv\Lib\site-packages\PIL\ImageStat.py:1(<module>)
        1    0.000    0.000    0.000    0.000 E:\Miniconda3\envs\lyrc-mv\Lib\site-packages\PIL\PngImagePlugin.py:106(Disposal)
        1    0.000    0.000    0.000    0.000 E:\Miniconda3\envs\lyrc-mv\Lib\site-packages\PIL\PngImagePlugin.py:975(load_prepare)
        1    0.000    0.000    0.000    0.000 E:\Miniconda3\envs\lyrc-mv\Lib\site-packages\PIL\GimpGradientFile.py:1(<module>)
        1    0.000    0.000    0.000    0.000 E:\Miniconda3\envs\lyrc-mv\Lib\site-packages\PIL\ImageFile.py:412(load_prepare)
        1    0.000    0.000    0.000    0.000 E:\Miniconda3\envs\lyrc-mv\Lib\site-packages\PIL\PngImagePlugin.py:127(Blend)
       17    0.000    0.000    0.000    0.000 E:\Miniconda3\envs\lyrc-mv\Lib\site-packages\PIL\_util.py:9(is_path)
        1    0.000    0.000    0.000    0.000 {built-in method PIL._imaging.new}
        2    0.000    0.000    0.000    0.000 E:\Miniconda3\envs\lyrc-mv\Lib\site-packages\PIL\ImageMode.py:37(getmode)
        2    0.000    0.000    0.000    0.000 E:\Miniconda3\envs\lyrc-mv\Lib\site-packages\PIL\Image.py:1382(getbands)
        4    0.000    0.000    0.000    0.000 E:\Miniconda3\envs\lyrc-mv\Lib\site-packages\PIL\PngImagePlugin.py:203(crc)
        1    0.000    0.000    0.000    0.000 E:\Miniconda3\envs\lyrc-mv\Lib\site-packages\PIL\GimpPaletteFile.py:1(<module>)
        1    0.000    0.000    0.000    0.000 E:\Miniconda3\envs\lyrc-mv\Lib\site-packages\PIL\ImageSequence.py:1(<module>)
        1    0.000    0.000    0.000    0.000 E:\Miniconda3\envs\lyrc-mv\Lib\site-packages\PIL\PaletteFile.py:1(<module>)
        3    0.000    0.000    0.000    0.000 E:\Miniconda3\envs\lyrc-mv\Lib\site-packages\PIL\Image.py:3757(register_extensions)
       14    0.000    0.000    0.000    0.000 E:\Miniconda3\envs\lyrc-mv\Lib\site-packages\PIL\Image.py:3746(register_extension)
       18    0.000    0.000    0.000    0.000 E:\Miniconda3\envs\lyrc-mv\Lib\site-packages\PIL\_binary.py:94(i32be)
        6    0.000    0.000    0.000    0.000 E:\Miniconda3\envs\lyrc-mv\Lib\site-packages\PIL\Image.py:3681(register_open)
        1    0.000    0.000    0.000    0.000 E:\Miniconda3\envs\lyrc-mv\Lib\site-packages\PIL\ImageChops.py:1(<module>)
        1    0.000    0.000    0.000    0.000 E:\Miniconda3\envs\lyrc-mv\Lib\site-packages\PIL\ImageStat.py:31(Stat)
        1    0.000    0.000    0.000    0.000 E:\Miniconda3\envs\lyrc-mv\Lib\site-packages\PIL\PngImagePlugin.py:1018(load_end)
        8    0.000    0.000    0.000    0.000 E:\Miniconda3\envs\lyrc-mv\Lib\site-packages\PIL\PngImagePlugin.py:150(_crc32)
        1    0.000    0.000    0.000    0.000 E:\Miniconda3\envs\lyrc-mv\Lib\site-packages\PIL\JpegPresets.py:1(<module>)
        1    0.000    0.000    0.000    0.000 E:\Miniconda3\envs\lyrc-mv\Lib\site-packages\PIL\BmpImagePlugin.py:58(_dib_accept)
        1    0.000    0.000    0.000    0.000 {built-in method PIL._imaging.zip_decoder}
        1    0.000    0.000    0.000    0.000 E:\Miniconda3\envs\lyrc-mv\Lib\site-packages\PIL\PngImagePlugin.py:381(PngStream)
        1    0.000    0.000    0.000    0.000 E:\Miniconda3\envs\lyrc-mv\Lib\site-packages\PIL\_binary.py:60(i32le)
        1    0.000    0.000    0.000    0.000 E:\Miniconda3\envs\lyrc-mv\Lib\site-packages\PIL\GifImagePlugin.py:80(GifImageFile)
        1    0.000    0.000    0.000    0.000 E:\Miniconda3\envs\lyrc-mv\Lib\site-packages\PIL\PngImagePlugin.py:448(chunk_IHDR)
        1    0.000    0.000    0.000    0.000 E:\Miniconda3\envs\lyrc-mv\Lib\site-packages\PIL\BmpImagePlugin.py:65(BmpImageFile)
        1    0.000    0.000    0.000    0.000 E:\Miniconda3\envs\lyrc-mv\Lib\site-packages\PIL\ImageStat.py:91(count)
        1    0.000    0.000    0.000    0.000 E:\Miniconda3\envs\lyrc-mv\Lib\site-packages\PIL\ImagePalette.py:31(ImagePalette)
        1    0.000    0.000    0.000    0.000 E:\Miniconda3\envs\lyrc-mv\Lib\site-packages\PIL\ImageMath.py:27(_Operand)
        1    0.000    0.000    0.000    0.000 E:\Miniconda3\envs\lyrc-mv\Lib\site-packages\PIL\ImageFile.py:113(ImageFile)
        1    0.000    0.000    0.000    0.000 E:\Miniconda3\envs\lyrc-mv\Lib\site-packages\PIL\PngImagePlugin.py:382(__init__)
        1    0.000    0.000    0.000    0.000 E:\Miniconda3\envs\lyrc-mv\Lib\site-packages\PIL\PngImagePlugin.py:515(chunk_gAMA)
        4    0.000    0.000    0.000    0.000 E:\Miniconda3\envs\lyrc-mv\Lib\site-packages\PIL\ImageFile.py:687(_safe_read)
        1    0.000    0.000    0.000    0.000 E:\Miniconda3\envs\lyrc-mv\Lib\site-packages\PIL\PngImagePlugin.py:469(chunk_IDAT)
        6    0.000    0.000    0.000    0.000 E:\Miniconda3\envs\lyrc-mv\Lib\site-packages\PIL\Image.py:3719(register_save)
        1    0.000    0.000    0.000    0.000 E:\Miniconda3\envs\lyrc-mv\Lib\site-packages\PIL\PngImagePlugin.py:752(PngImageFile)
        1    0.000    0.000    0.000    0.000 E:\Miniconda3\envs\lyrc-mv\Lib\site-packages\PIL\GimpPaletteFile.py:23(GimpPaletteFile)
        6    0.000    0.000    0.000    0.000 E:\Miniconda3\envs\lyrc-mv\Lib\site-packages\PIL\Image.py:3704(register_mime)
        1    0.000    0.000    0.000    0.000 E:\Miniconda3\envs\lyrc-mv\Lib\site-packages\PIL\PngImagePlugin.py:549(chunk_pHYs)
        5    0.000    0.000    0.000    0.000 {built-in method PIL._imaging.draw}
        1    0.000    0.000    0.000    0.000 E:\Miniconda3\envs\lyrc-mv\Lib\site-packages\PIL\ImageFile.py:722(PyCodecState)
        1    0.000    0.000    0.000    0.000 E:\Miniconda3\envs\lyrc-mv\Lib\site-packages\PIL\BmpImagePlugin.py:54(_accept)
        1    0.000    0.000    0.000    0.000 E:\Miniconda3\envs\lyrc-mv\Lib\site-packages\PIL\PngImagePlugin.py:252(iTXt)
        1    0.000    0.000    0.000    0.000 E:\Miniconda3\envs\lyrc-mv\Lib\site-packages\PIL\BmpImagePlugin.py:325(BmpRleDecoder)
        1    0.000    0.000    0.000    0.000 E:\Miniconda3\envs\lyrc-mv\Lib\site-packages\PIL\PngImagePlugin.py:532(chunk_sRGB)
        1    0.000    0.000    0.000    0.000 E:\Miniconda3\envs\lyrc-mv\Lib\site-packages\PIL\ImageFile.py:457(StubImageFile)
        1    0.000    0.000    0.000    0.000 E:\Miniconda3\envs\lyrc-mv\Lib\site-packages\PIL\ImageFile.py:487(Parser)
        2    0.000    0.000    0.000    0.000 E:\Miniconda3\envs\lyrc-mv\Lib\site-packages\PIL\Image.py:3732(register_save_all)
        1    0.000    0.000    0.000    0.000 E:\Miniconda3\envs\lyrc-mv\Lib\site-packages\PIL\ImageFile.py:811(PyDecoder)
        1    0.000    0.000    0.000    0.000 E:\Miniconda3\envs\lyrc-mv\Lib\site-packages\PIL\PngImagePlugin.py:158(ChunkStream)
        2    0.000    0.000    0.000    0.000 E:\Miniconda3\envs\lyrc-mv\Lib\site-packages\PIL\PngImagePlugin.py:744(_accept)
        1    0.000    0.000    0.000    0.000 E:\Miniconda3\envs\lyrc-mv\Lib\site-packages\PIL\ImageFile.py:448(StubHandler)
        1    0.000    0.000    0.000    0.000 E:\Miniconda3\envs\lyrc-mv\Lib\site-packages\PIL\ImageFile.py:865(PyEncoder)
        1    0.000    0.000    0.000    0.000 E:\Miniconda3\envs\lyrc-mv\Lib\site-packages\PIL\PpmImagePlugin.py:163(PpmPlainDecoder)
        1    0.000    0.000    0.000    0.000 E:\Miniconda3\envs\lyrc-mv\Lib\site-packages\PIL\PngImagePlugin.py:279(PngInfo)
        1    0.000    0.000    0.000    0.000 E:\Miniconda3\envs\lyrc-mv\Lib\site-packages\PIL\ImageFilter.py:184(__init__)
        1    0.000    0.000    0.000    0.000 E:\Miniconda3\envs\lyrc-mv\Lib\site-packages\PIL\ImageFile.py:733(PyCodec)
        1    0.000    0.000    0.000    0.000 E:\Miniconda3\envs\lyrc-mv\Lib\site-packages\PIL\GifImagePlugin.py:71(_accept)
        1    0.000    0.000    0.000    0.000 E:\Miniconda3\envs\lyrc-mv\Lib\site-packages\PIL\ImageFile.py:101(_Tile)
        1    0.000    0.000    0.000    0.000 E:\Miniconda3\envs\lyrc-mv\Lib\site-packages\PIL\ImageSequence.py:24(Iterator)
        1    0.000    0.000    0.000    0.000 E:\Miniconda3\envs\lyrc-mv\Lib\site-packages\PIL\PpmImagePlugin.py:57(PpmImageFile)
        3    0.000    0.000    0.000    0.000 E:\Miniconda3\envs\lyrc-mv\Lib\site-packages\PIL\Image.py:3778(register_decoder)
        1    0.000    0.000    0.000    0.000 E:\Miniconda3\envs\lyrc-mv\Lib\site-packages\PIL\PpmImagePlugin.py:49(_accept)
        1    0.000    0.000    0.000    0.000 E:\Miniconda3\envs\lyrc-mv\Lib\site-packages\PIL\ImageEnhance.py:25(_Enhance)
        1    0.000    0.000    0.000    0.000 E:\Miniconda3\envs\lyrc-mv\Lib\site-packages\PIL\GimpGradientFile.py:67(GradientFile)
        1    0.000    0.000    0.000    0.000 E:\Miniconda3\envs\lyrc-mv\Lib\site-packages\PIL\PaletteFile.py:22(PaletteFile)
        1    0.000    0.000    0.000    0.000 E:\Miniconda3\envs\lyrc-mv\Lib\site-packages\PIL\GifImagePlugin.py:647(_Frame)
        1    0.000    0.000    0.000    0.000 E:\Miniconda3\envs\lyrc-mv\Lib\site-packages\PIL\JpegImagePlugin.py:327(_accept)
        1    0.000    0.000    0.000    0.000 E:\Miniconda3\envs\lyrc-mv\Lib\site-packages\PIL\PngImagePlugin.py:1152(_Frame)
        1    0.000    0.000    0.000    0.000 E:\Miniconda3\envs\lyrc-mv\Lib\site-packages\PIL\PngImagePlugin.py:375(_RewindState)
        1    0.000    0.000    0.000    0.000 E:\Miniconda3\envs\lyrc-mv\Lib\site-packages\PIL\BmpImagePlugin.py:401(DibImageFile)
        1    0.000    0.000    0.000    0.000 E:\Miniconda3\envs\lyrc-mv\Lib\site-packages\PIL\GimpGradientFile.py:115(GimpGradientFile)
        1    0.000    0.000    0.000    0.000 E:\Miniconda3\envs\lyrc-mv\Lib\site-packages\PIL\ImageOps.py:417(SupportsGetMesh)
        1    0.000    0.000    0.000    0.000 E:\Miniconda3\envs\lyrc-mv\Lib\site-packages\PIL\PngImagePlugin.py:159(__init__)
        1    0.000    0.000    0.000    0.000 E:\Miniconda3\envs\lyrc-mv\Lib\site-packages\PIL\PngImagePlugin.py:1128(_idat)
        1    0.000    0.000    0.000    0.000 E:\Miniconda3\envs\lyrc-mv\Lib\site-packages\PIL\ImageEnhance.py:43(Color)
        1    0.000    0.000    0.000    0.000 E:\Miniconda3\envs\lyrc-mv\Lib\site-packages\PIL\PpmImagePlugin.py:300(PpmDecoder)
        1    0.000    0.000    0.000    0.000 E:\Miniconda3\envs\lyrc-mv\Lib\site-packages\PIL\PngImagePlugin.py:190(close)
        1    0.000    0.000    0.000    0.000 E:\Miniconda3\envs\lyrc-mv\Lib\site-packages\PIL\PngImagePlugin.py:1139(_fdat)
        1    0.000    0.000    0.000    0.000 E:\Miniconda3\envs\lyrc-mv\Lib\site-packages\PIL\ImageEnhance.py:63(Contrast)
        1    0.000    0.000    0.000    0.000 E:\Miniconda3\envs\lyrc-mv\Lib\site-packages\PIL\ImageEnhance.py:100(Sharpness)
        1    0.000    0.000    0.000    0.000 E:\Miniconda3\envs\lyrc-mv\Lib\site-packages\PIL\ImageEnhance.py:84(Brightness)
        1    0.000    0.000    0.000    0.000 E:\Miniconda3\envs\lyrc-mv\Lib\site-packages\PIL\ImageFile.py:347(<lambda>)
        1    0.000    0.000    0.000    0.000 E:\Miniconda3\envs\lyrc-mv\Lib\site-packages\PIL\ImageFile.py:96(_tilesort)




Compose相关:
         436642 function calls (413139 primitive calls) in 39.398 seconds

   Ordered by: cumulative time
   List reduced from 1162 to 11 due to restriction <'compose'>

   ncalls  tottime  percall  cumtime  percall filename:lineno(function)
      888    2.002    0.002   15.583    0.018 E:\Miniconda3\envs\lyrc-mv\Lib\site-packages\moviepy\video\VideoClip.py:720(compose_on)
      888    8.325    0.009   10.418    0.012 E:\Miniconda3\envs\lyrc-mv\Lib\site-packages\moviepy\video\VideoClip.py:796(compose_mask)
        1    0.000    0.000    0.004    0.004 E:\Miniconda3\envs\lyrc-mv\Lib\site-packages\yaml\composer.py:29(get_single_node)
        1    0.000    0.000    0.004    0.004 E:\Miniconda3\envs\lyrc-mv\Lib\site-packages\yaml\composer.py:50(compose_document)
     27/1    0.000    0.000    0.004    0.004 E:\Miniconda3\envs\lyrc-mv\Lib\site-packages\yaml\composer.py:63(compose_node)
      3/1    0.000    0.000    0.003    0.003 E:\Miniconda3\envs\lyrc-mv\Lib\site-packages\yaml\composer.py:117(compose_mapping_node)
       24    0.000    0.000    0.000    0.000 E:\Miniconda3\envs\lyrc-mv\Lib\site-packages\yaml\composer.py:88(compose_scalar_node)
        1    0.000    0.000    0.000    0.000 E:\Miniconda3\envs\lyrc-mv\Lib\site-packages\yaml\composer.py:1(<module>)
        1    0.000    0.000    0.000    0.000 E:\Miniconda3\envs\lyrc-mv\Lib\site-packages\yaml\composer.py:11(Composer)
        1    0.000    0.000    0.000    0.000 E:\Miniconda3\envs\lyrc-mv\Lib\site-packages\yaml\composer.py:13(__init__)
        1    0.000    0.000    0.000    0.000 E:\Miniconda3\envs\lyrc-mv\Lib\site-packages\yaml\composer.py:8(ComposerError)




get_frame相关:
         436642 function calls (413139 primitive calls) in 39.398 seconds

   Ordered by: cumulative time
   List reduced from 1162 to 3 due to restriction <'get_frame'>

   ncalls  tottime  percall  cumtime  percall filename:lineno(function)
 6443/711    0.010    0.000   32.362    0.046 <decorator-gen-17>:1(get_frame)
 6443/711    0.189    0.000   32.348    0.045 E:\Miniconda3\envs\lyrc-mv\Lib\site-packages\moviepy\Clip.py:66(get_frame)
      221    0.037    0.000    0.060    0.000 E:\Miniconda3\envs\lyrc-mv\Lib\site-packages\moviepy\audio\io\readers.py:188(get_frame)


