"""
增强的 write_videofile 性能分析脚本
"""
import time
import sys
from pathlib import Path

# 添加当前目录到路径
sys.path.insert(0, str(Path(__file__).parent))

from enhanced_generator import demo_enhanced_features, EnhancedJingwuGenerator
from lrc_mv_config import load_lrc_mv_config


def analyze_write_videofile_performance():
    """详细分析 write_videofile 的性能"""
    
    print("write_videofile 性能分析")
    print("="*50)
    
    config_path = Path(r"精武英雄\lrc-mv.yaml")
    
    if not config_path.exists():
        print(f"❌ 配置文件不存在: {config_path}")
        return False
    
    try:
        # 加载配置
        config = load_lrc_mv_config(str(config_path))
        
        print(f"分析配置:")
        print(f"  - 视频尺寸: {config.width}x{config.height}")
        print(f"  - 音频文件: {config.audio}")
        print(f"  - 输出文件: {config.output}")
        
        # 测试不同的配置
        test_configs = [
            {"name": "草稿质量 + 短时长", "duration": 10, "profile": "draft"},
            {"name": "草稿质量 + 中等时长", "duration": 20, "profile": "draft"},
            {"name": "产品质量 + 短时长", "duration": 10, "profile": "production"},
        ]
        
        results = []
        
        for test_config in test_configs:
            print(f"\n🔍 测试: {test_config['name']}")
            print("-" * 30)
            
            start_time = time.perf_counter()
            
            success = demo_enhanced_features(
                config_path=config_path,
                t_max_sec=test_config["duration"],
                quality_profile=test_config["profile"]
            )
            
            end_time = time.perf_counter()
            duration = end_time - start_time
            
            result = {
                "config": test_config,
                "success": success,
                "duration": duration,
                "speed_ratio": test_config["duration"] / duration if duration > 0 else 0
            }
            
            results.append(result)
            
            print(f"✅ 完成: {duration:.2f}秒 (速度比: {result['speed_ratio']:.2f}x)")
            
            if not success:
                print("❌ 测试失败")
                break
        
        # 分析结果
        print("\n" + "="*50)
        print("性能分析结果")
        print("="*50)
        
        for result in results:
            config = result["config"]
            print(f"\n{config['name']}:")
            print(f"  - 视频时长: {config['duration']}秒")
            print(f"  - 处理时间: {result['duration']:.2f}秒")
            print(f"  - 处理速度: {result['speed_ratio']:.2f}x 实时")
            print(f"  - 质量配置: {config['profile']}")
            
            if result['speed_ratio'] < 0.5:
                print(f"  ⚠️  处理速度较慢 (< 0.5x)")
            elif result['speed_ratio'] < 1.0:
                print(f"  📝 处理速度一般 (< 1x)")
            else:
                print(f"  ✅ 处理速度良好 (>= 1x)")
        
        # 性能建议
        print("\n" + "="*50)
        print("性能优化建议")
        print("="*50)
        
        if results:
            draft_results = [r for r in results if r["config"]["profile"] == "draft"]
            prod_results = [r for r in results if r["config"]["profile"] == "production"]
            
            if draft_results and prod_results:
                draft_avg = sum(r["speed_ratio"] for r in draft_results) / len(draft_results)
                prod_avg = sum(r["speed_ratio"] for r in prod_results) / len(prod_results)
                
                print(f"1. 草稿质量平均速度: {draft_avg:.2f}x")
                print(f"2. 产品质量平均速度: {prod_avg:.2f}x")
                print(f"3. 质量差异影响: {prod_avg/draft_avg:.2f}x" if draft_avg > 0 else "")
        
        print("4. 建议检查项目:")
        print("   - FFmpeg 编码器选择 (CPU vs GPU)")
        print("   - 视频片段数量和复杂度")
        print("   - 音频处理时间")
        print("   - 磁盘 I/O 性能")
        
        return True
        
    except Exception as e:
        print(f"❌ 分析失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def install_py_spy():
    """安装 py-spy 用于深度分析"""
    print("\n🔧 安装 py-spy 进行深度分析...")
    
    try:
        import subprocess
        result = subprocess.run([
            sys.executable, "-m", "pip", "install", "py-spy"
        ], capture_output=True, text=True)
        
        if result.returncode == 0:
            print("✅ py-spy 安装成功")
            print("\n使用方法:")
            print("1. 在一个终端运行: python enhanced_generator.py")
            print("2. 在另一个终端运行: py-spy top --pid <进程ID>")
            print("3. 或生成火焰图: py-spy record -o flamegraph.svg --pid <进程ID>")
            return True
        else:
            print(f"❌ py-spy 安装失败: {result.stderr}")
            return False
    except Exception as e:
        print(f"❌ py-spy 安装失败: {e}")
        return False


if __name__ == "__main__":
    print("MoviePy write_videofile 性能诊断工具")
    print("="*50)
    
    # 运行性能分析
    success = analyze_write_videofile_performance()
    
    if success:
        print("\n🎯 想要更深入的分析吗?")
        choice = input("是否安装 py-spy 进行代码级别的性能分析? (y/n): ").lower()
        
        if choice == 'y':
            install_py_spy()
    
    print("\n完成!")
